package types

type UserInfo struct {
	CID string `json:"cid"` // 客户端ID
	NID string `json:"nid"` // 所在的节点服务ID
	UID string `json:"uid"` // 用户唯一标识
}

type ClientBasicInfo struct {
	Cid             string `json:"cid"`               // 客户端ID
	Nid             string `json:"nid"`               // 所在的节点服务ID
	Product         string `json:"product"`           // 产品名称
	Type            string `json:"type"`              // 客户端类型
	Status          string `json:"status"`            // 【待定】客户端状态
	CreatedAt       int64  `json:"created_at"`        // 客户端的创建时间
	LastCreatedAt   int64  `json:"last_created_at"`   // 最近一次客户端的创建时间
	LastRequestedAt int64  `json:"last_requested_at"` // 最近一次请求时间
	NumOfRequest    uint64 `json:"num_of_request"`    // 【待定】请求次数
}

type ClientInfo struct {
	ClientBasicInfo

	CustomFields map[string]any `json:"custom_fields"` // 客户端的响应自定义字段
	LoginResp    map[string]any `json:"login_resp"`    // 登录响应信息
	UserInfo     *UserInfo      `json:"user_info"`     // 用户基础信息
}

type CreateClientReq struct {
	Type         string         `json:"type" validate:"required"`
	Url          string         `json:"url,omitempty,optional"`
	CustomFields map[string]any `json:"custom_fields,omitempty,optional"`

	TimeoutHeaders // 自定义超时时间
}

type CreateClientResp struct {
	*ClientInfo
}

type DeleteClientReq struct {
	Cid string `form:"cid" validate:"required" zh:"客户端ID"`
}

type DeleteClientResp struct {
	*ClientInfo
}

type ViewClientReq struct {
	Cid string `form:"cid" validate:"required" zh:"客户端ID"`
}

type ViewClientResp struct {
	*ClientInfo
}
