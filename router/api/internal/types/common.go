package types

type ExtraOptions struct {
	UnUseJSONNumber bool `json:"un_use_json_number,omitempty,optional"`
}

type TimeoutHeaders struct {
	ConnectTimeout  *int64 `header:"X-API-Proxy-Connect-Timeout,omitempty,optional"`
	RequestTimeout  *int64 `header:"X-API-Proxy-Request-Timeout,omitempty,optional"`
	ResponseTimeout *int64 `header:"X-API-Proxy-Response-Timeout,omitempty,optional"`
}

type CallResp struct {
	Header map[string][]string `json:"header"` // 响应头
	Body   any                 `json:"body"`   // 响应体
	Status int32               `json:"status"` // 响应状态码
}

type ApiCallReq struct {
	Cid          string              `json:"cid" validate:"required"`          // 客户端ID
	Url          string              `json:"url,omitempty,optional"`           // 请求路径
	Method       string              `json:"method,omitempty,optional"`        // 请求方法
	Headers      map[string][]string `json:"headers,omitempty,optional"`       // 请求头
	Body         any                 `json:"body,omitempty,optional"`          // 请求体
	Authority    string              `json:"authority,omitempty,optional"`     // gRPC服务器名称
	CustomFields map[string]any      `json:"custom_fields,omitempty,optional"` // 客户端的请求自定义字段
	ExtraOptions ExtraOptions        `json:"extra_options,omitempty,optional"` // 额外选项

	TimeoutHeaders // 自定义超时时间
}

type ApiCallResp struct {
	ClientInfo   *ClientBasicInfo `json:"client_info"`
	CustomFields map[string]any   `json:"custom_fields"`
	CallResp     *CallResp        `json:"call_resp"`
}

type ProjectConfig struct {
	ProjectName string `json:"project_name" validate:"required"` // 项目名称
	GitConfig
	ExcludePaths []string            `json:"exclude_paths,omitempty,optional"` // 排除的路径
	ExcludeFiles []string            `json:"exclude_files,omitempty,optional"` // 排除的文件
	Dependencies []*DependenceConfig `json:"dependencies,omitempty,optional"`  // 依赖配置
}

type DependenceConfig struct {
	Git       *GitConfig `json:"git,omitempty,optional"`
	LocalPath string     `json:"local_path,omitempty,optional"`
}

type GitConfig struct {
	GitURL     string `json:"git_url" validate:"required"`               // Git地址
	Branch     string `json:"branch,default=main" validate:"required"`   // 分支名称
	ImportPath string `json:"import_path,default=." validate:"required"` // 导入路径
}

type ProjectInfo struct {
	ProjectName string  `json:"project_name"` // 项目名称
	Branch      string  `json:"branch"`       // 分支名称
	Commit      *Commit `json:"commit"`       // 提交信息
}

type Commit struct {
	Hash    string `json:"hash"`
	Author  string `json:"author"`
	Date    string `json:"date"`
	Message string `json:"message"`
}

type UpdateProtoReq struct {
	ProductName string           `json:"product_name" validate:"required"`                                     // 产品名称
	Branch      string           `json:"branch,default=main" validate:"required"`                              // 分支名称
	Projects    []*ProjectConfig `json:"projects,omitempty,optional" validate:"omitempty,gte=1,dive,required"` // 项目配置
}

type UpdateProtoResp struct {
	ProductName string         `json:"product_name"` // 产品名称
	Branch      string         `json:"branch"`       // 分支
	Projects    []*ProjectInfo `json:"projects"`     // 项目信息
}

type JsonToProtoReq struct {
	ProductName string `json:"product_name" validate:"required"` // 产品名称
	Message     string `json:"message" validate:"required"`      // 目标消息名称
	Data        any    `json:"data" validate:"required"`         // 待转换数据
}

type JsonToProtoResp struct {
	ProtoString string `json:"proto_string"` // 转换后的数据
}

type ProtoToJsonReq struct {
	ProductName string `json:"product_name" validate:"required"` // 产品名称
	Message     string `json:"message" validate:"required"`      // 目标消息名称
	Data        string `json:"data" validate:"required"`         // 待转换数据
}

type ProtoToJsonResp struct {
	JsonData any `json:"json_data"` // 转换后的数据
}
