syntax = "proto3";

package common;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.ttyuyin.com/TestDevelopment/api-proxy/common/pb";

// ServerInfo 服务基础信息（包括：路由服务、节点服务）
message ServerInfo {
  string key = 1; // 服务Key名称
  string server_id = 2; // 服务ID
  string host = 3; // 服务地址
  int32 port = 4; // 服务监听端口
  string registry = 5; // 注册中心（如：redis://host/db）
  google.protobuf.Timestamp started_at = 6; // 服务启动时间
}

// RouterServerInfo 路由服务基础信息
message RouterServerInfo {
  message Metadata {}

  ServerInfo info = 1; // 路由服务信息
  Metadata metadata = 2; // 路由服务元数据
}

// NodeServerInfo 节点服务基础信息
message NodeServerInfo {
  message Metadata {
    int64 clients = 1; // 客户端数量
    int64 users = 2; // 用户数量
  }

  ServerInfo info = 1; // 节点服务信息
  Metadata metadata = 2; // 节点服务元数据
}

// ClientInfo 客户端基础信息
message ClientInfo {
  string cid = 1; // 客户端ID
  string nid = 2; // 所在的节点服务ID
  string product = 3; // 产品名称
  string type = 4; // 客户端类型
  string status = 5; // 客户端状态
  google.protobuf.Timestamp created_at = 6; // 客户端的创建时间
  google.protobuf.Timestamp last_created_at = 7; // 最近一次客户端的创建时间
  google.protobuf.Timestamp last_requested_at = 8; // 最近一次请求时间
  uint64 num_of_request = 9; // 请求次数
  // @gotags: json:"custom_fields,omitempty" copier:"CustomFields"
  google.protobuf.Struct custom_info = 10; // 客户端自定义信息，注：为了兼容原接口定义，故把序列化字段名称设置为`custom_fields`
  google.protobuf.Struct login_resp = 11; // 登录响应信息
  UserInfo user_info = 12; // 用户基础信息
  CreateInfo create_info = 13; // 客户端的创建信息
}

// CreateInfo 客户端的创建信息
message CreateInfo {
  string type = 1; // 客户端类型
  string url = 2; // 服务端连接地址
  google.protobuf.Struct custom_fields = 3; // 客户端的请求自定义字段
}

// UserInfo 用户基础信息
message UserInfo {
  string cid = 1; // 客户端ID
  string nid = 2; // 所在的节点服务ID
  string uid = 3; // 用户唯一标识
}

// AttrInfo 解析器地址的属性信息
message AttrInfo {
  ServerInfo node = 1; // 节点服务信息
  map<string, ClientInfo> clients = 2; // 节点服务下全部客户端的信息
  map<string, UserInfo> users = 3; // 节点服务下各客户端对应的用户信息
}

// TimeoutInfo 超时信息
message TimeoutInfo {
  optional int64 connect_timeout = 1; // 连接超时时间
  optional int64 request_timeout = 2; // 请求超时时间
  optional int64 response_timeout = 3; // 响应超时时间
}
