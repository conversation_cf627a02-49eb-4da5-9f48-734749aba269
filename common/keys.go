package common

const (
	ConnectTimeoutHeaderKey  = "X-API-Proxy-Connect-Timeout"
	RequestTimeoutHeaderKey  = "X-API-Proxy-Request-Timeout"
	ResponseTimeoutHeaderKey = "X-API-Proxy-Response-Timeout"

	ProductNameHeaderKey = "X-API-Proxy-Product-Name"
	ClientTypeHeaderKey  = "X-API-Proxy-Client-Type"

	RateLimitAllowedHeaderKey    = "X-RateLimit-Allowed"
	RateLimitRemainingHeaderKey  = "X-RateLimit-Remaining"
	RateLimitRetryAfterHeaderKey = "X-RateLimit-RetryAfter"
	RateLimitResetAfterHeaderKey = "X-RateLimit-ResetAfter"
)

type (
	redisConfContextKey     struct{}
	usernameContextKey      struct{}
	clientIDContextKey      struct{}
	clientInfoContextKey    struct{}
	timeoutInfoContextKey   struct{}
	customHeadersContextKey struct{}

	nodeInfoAttributeKey     struct{}
	nodeAttrInfoAttributeKey struct{}
)

var (
	RedisConfContextKey     redisConfContextKey
	UsernameContextKey      usernameContextKey
	ClientIDContextKey      clientIDContextKey
	ClientInfoContextKey    clientInfoContextKey // Deprecated: use CustomHeadersContextKey instead.
	TimeoutInfoContextKey   timeoutInfoContextKey
	CustomHeadersContextKey customHeadersContextKey

	NodeInfoAttributeKey     nodeInfoAttributeKey
	NodeAttrInfoAttributeKey nodeAttrInfoAttributeKey
)
