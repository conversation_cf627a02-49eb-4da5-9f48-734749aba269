package common

const (
	ServiceName       = "api-proxy"
	RouterServiceName = "router"
	NodeServiceName   = "node"
)

const (
	ConstServerIDLogFieldKey      = "server_id"
	ConstClientInfoMetadataKey    = "client_info"
	ConstCustomTimeoutMetadataKey = "custom_timeout"

	ConstServerIDPrefix = "server_id:"
	ConstClientIDPrefix = "client_id:"

	ConstClientInfoKeyPrefix = "client:info:"
	ConstUserInfoKeyPrefix   = "user:info:"

	ConstClientsInfoKey          = "clients:info:" + string(constRedisTypeHash)
	ConstUsersInfoKey            = "users:info:" + string(constRedisTypeHash)
	ConstUnmanagedClientsInfoKey = "unmanaged_clients:info:" + string(constRedisTypeHash)

	ConstAddressSeparator = ","
	ConstPort80           = 80
	ConstPort8080         = 8080

	ConstHashMapCapacity = 64
)

type redisType string

const (
	constRedisTypeString    redisType = "string"
	constRedisTypeHash      redisType = "hash"
	constRedisTypeList      redisType = "list"
	constRedisTypeSet       redisType = "set"
	constRedisTypeSortedSet redisType = "zset"
	constRedisTypeStream    redisType = "stream"
)
